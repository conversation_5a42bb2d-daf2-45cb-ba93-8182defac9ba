/* Enhanced Styles for Cyber Wolf Email Template */

:root {
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #FF6B6B;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --text-color: #333;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --shadow: 0 4px 15px rgba(0,0,0,0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Animation Keyframes */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--accent-color); }
    50% { box-shadow: 0 0 20px var(--accent-color), 0 0 30px var(--accent-color); }
}

/* Enhanced Email Container */
.email-container {
    animation: fadeIn 0.6s ease-out;
    position: relative;
}

/* Enhanced Header with Animated Background */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), #0f3460);
    position: relative;
    overflow: hidden;
}

.header::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.03) 2px,
        rgba(255,255,255,0.03) 4px
    );
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Wolf Icon */
.wolf-icon {
    animation: pulse 2s infinite;
    filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
}

/* Enhanced Congratulations Section */
.congratulations {
    animation: glow 2s infinite alternate;
    position: relative;
    overflow: hidden;
}

.congratulations::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Countdown Timer */
.countdown {
    position: relative;
    overflow: hidden;
}

.countdown-timer {
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 10px rgba(255,255,255,0.5);
    animation: pulse 1s infinite;
}

/* Enhanced Detail Items */
.detail-item {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.detail-item:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(26,26,46,0.1), transparent);
    transition: left 0.5s ease;
}

.detail-item:hover::before {
    left: 100%;
}

/* Enhanced Detail Icons */
.detail-icon {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.detail-item:hover .detail-icon {
    transform: rotate(360deg);
    box-shadow: 0 0 20px rgba(26,26,46,0.3);
}

/* Enhanced CTA Buttons */
.cta-button {
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

/* Enhanced Feature Cards */
.feature-card {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover::before {
    opacity: 0.05;
}

.feature-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 30px rgba(255,107,107,0.2);
}

/* Enhanced Feature Icons */
.feature-icon {
    transition: var(--transition);
    position: relative;
    z-index: 1;
}

.feature-card:hover .feature-icon {
    transform: scale(1.2) rotate(10deg);
    color: var(--accent-color);
}

/* Enhanced Notice Board */
.notice-board {
    position: relative;
    animation: fadeIn 0.8s ease-out 0.5s both;
}

.notice-board::before {
    content: '📌';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 24px;
    animation: pulse 2s infinite;
}

/* Enhanced Footer */
.footer {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--accent-color));
}

/* Enhanced Social Links */
.social-link {
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
}

.social-link:hover {
    background: var(--accent-color);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(255,107,107,0.4);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.info {
    background: var(--success-color);
}

.notification.urgent {
    background: var(--accent-color);
    animation: slideIn 0.3s ease, pulse 1s infinite 0.3s;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255,255,255,0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color));
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .feature-card:hover {
        transform: none;
    }
    
    .detail-item:hover {
        transform: none;
    }
    
    .cta-button {
        width: 100%;
        margin: 5px 0;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        margin: 0 5px;
    }
}

@media (max-width: 480px) {
    .countdown-timer {
        font-size: 18px;
    }
    
    .feature-icon {
        font-size: 30px;
    }
    
    .detail-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

/* Print Styles */
@media print {
    .email-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .header {
        background: var(--primary-color) !important;
        -webkit-print-color-adjust: exact;
    }
    
    .cta-button {
        border: 2px solid var(--accent-color);
        color: var(--accent-color) !important;
        background: transparent !important;
    }
    
    .countdown, .social-links {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #e0e0e0;
        --light-bg: #2a2a2a;
        --white: #1e1e1e;
    }
    
    .email-container {
        background: var(--white);
        color: var(--text-color);
    }
    
    .detail-item {
        background: #2a2a2a;
        color: var(--text-color);
    }
    
    .feature-card {
        background: #2a2a2a;
        color: var(--text-color);
    }
}
