<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <title>Cyber Wolf - Free Training Course Registration Confirmation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #FF6B6B;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --text-color: #333;
            --light-bg: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 15px rgba(0,0,0,0.1);
            --border-radius: 10px;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Roboto', 'Arial', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px 0;
        }
        
        /* Animation Keyframes */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 107, 107, 0.3); }
            50% { box-shadow: 0 0 30px rgba(255, 107, 107, 0.6), 0 0 40px rgba(255, 107, 107, 0.4); }
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .email-container {
            max-width: 650px;
            margin: 20px auto;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out;
            position: relative;
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--warning-color), var(--accent-color));
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), #0f3460);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 32px;
            font-weight: 900;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 20px rgba(255,255,255,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        .wolf-icon {
            display: inline-block;
            margin-right: 15px;
            font-size: 36px;
            animation: bounce 2s infinite;
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.5));
        }

        .tagline {
            font-size: 18px;
            opacity: 0.95;
            position: relative;
            z-index: 1;
            font-weight: 300;
            letter-spacing: 1px;
            animation: slideIn 1s ease-out 0.5s both;
        }
        
        .content {
            padding: 30px;
        }
        
        .congratulations {
            background: linear-gradient(135deg, var(--success-color), #45a049);
            color: white;
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            animation: glow 3s ease-in-out infinite alternate;
        }

        .congratulations::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shine 2s infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .countdown {
            background: linear-gradient(135deg, var(--accent-color), #FF5252);
            color: white;
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 25px 0;
            position: relative;
            overflow: hidden;
        }

        .countdown::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: move 20s linear infinite;
        }

        @keyframes move {
            0% { transform: translateX(-20px); }
            100% { transform: translateX(20px); }
        }

        .countdown-timer {
            font-family: 'Orbitron', monospace;
            font-size: 28px;
            font-weight: 700;
            margin-top: 15px;
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
            animation: pulse 1s infinite;
            position: relative;
            z-index: 1;
        }
        
        .course-details {
            background: var(--light-bg);
            border-left: 4px solid var(--primary-color);
            padding: 30px;
            margin: 25px 0;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            position: relative;
        }

        .course-details::before {
            content: '📚';
            position: absolute;
            top: -10px;
            left: -10px;
            font-size: 24px;
            background: var(--white);
            padding: 8px;
            border-radius: 50%;
            box-shadow: var(--shadow);
        }

        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .detail-item:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .detail-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(26,26,46,0.05), transparent);
            transition: left 0.5s ease;
        }

        .detail-item:hover::before {
            left: 100%;
        }

        .detail-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(26,26,46,0.2);
            line-height: 1;
            text-align: center;
        }

        .detail-item:hover .detail-icon {
            transform: rotate(360deg) scale(1.1);
            box-shadow: 0 6px 20px rgba(26,26,46,0.3);
        }

        .detail-content {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        .detail-label {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .detail-value {
            color: #555;
            font-size: 17px;
            font-weight: 400;
            line-height: 1.4;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
            padding: 20px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, var(--accent-color), #FF5252);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 30px;
            font-weight: 600;
            font-size: 16px;
            margin: 12px;
            transition: var(--transition);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: none;
            cursor: pointer;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
        }

        .cta-button:active {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .cta-button.secondary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 6px 20px rgba(26, 26, 46, 0.3);
        }

        .cta-button.secondary:hover {
            box-shadow: 0 10px 30px rgba(26, 26, 46, 0.4);
        }

        .cta-button.calendar {
            background: linear-gradient(135deg, var(--success-color), #45a049);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
        }

        .cta-button.calendar:hover {
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 40px 0;
            padding: 0 10px;
        }

        .feature-card {
            background: var(--white);
            padding: 30px 25px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 0.05;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: var(--accent-color);
            box-shadow: 0 15px 40px rgba(255,107,107,0.2);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: var(--primary-color);
            transition: var(--transition);
            position: relative;
            z-index: 1;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.2) rotate(10deg);
            color: var(--accent-color);
        }

        .feature-card h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
            position: relative;
            z-index: 1;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .notice-board {
            background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
            border: 2px solid var(--warning-color);
            border-radius: var(--border-radius);
            padding: 25px;
            margin: 30px 0;
            position: relative;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .notice-board::before {
            content: '📌';
            position: absolute;
            top: -12px;
            right: -12px;
            font-size: 28px;
            animation: bounce 2s infinite;
            background: var(--white);
            border-radius: 50%;
            padding: 5px;
            box-shadow: var(--shadow);
        }

        .notice-title {
            color: #E65100;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            font-family: 'Orbitron', monospace;
        }

        .notice-board ul {
            margin-left: 25px;
            margin-top: 15px;
        }

        .notice-board li {
            margin-bottom: 8px;
            color: #BF360C;
            font-weight: 500;
        }
        
        .footer {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--warning-color), var(--accent-color));
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .footer h3 {
            font-family: 'Orbitron', monospace;
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 0 10px rgba(255,255,255,0.3);
        }

        .social-links {
            margin: 25px 0;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            margin: 0 10px;
            color: white;
            text-decoration: none;
            font-size: 24px;
            transition: var(--transition);
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            line-height: 1;
            text-align: center;
            vertical-align: middle;
        }

        .social-link:hover {
            color: white;
            background: var(--accent-color);
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 8px 25px rgba(255,107,107,0.4);
        }

        .footer p {
            margin: 10px 0;
            opacity: 0.9;
        }

        .footer .copyright {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.2);
            font-size: 13px;
            opacity: 0.7;
        }
        
        /* Enhanced Responsive Styles */
        @media (max-width: 768px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 30px 15px;
            }

            .logo {
                font-size: 28px;
            }

            .wolf-icon {
                font-size: 32px;
            }

            .tagline {
                font-size: 16px;
            }

            .content {
                padding: 20px 15px;
            }

            .countdown-timer {
                font-size: 24px;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .feature-card:hover {
                transform: none;
            }

            .detail-item {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .detail-item:hover {
                transform: none;
            }

            .detail-icon {
                margin-right: 0;
                margin-bottom: 15px;
                width: 45px;
                height: 45px;
                font-size: 22px;
            }

            .cta-button {
                display: block;
                width: 90%;
                margin: 10px auto;
                padding: 15px;
            }

            .social-link {
                width: 45px;
                height: 45px;
                margin: 0 8px;
                font-size: 20px;
                line-height: 1;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 25px 10px;
            }

            .logo {
                font-size: 24px;
            }

            .wolf-icon {
                font-size: 28px;
            }

            .tagline {
                font-size: 14px;
            }

            .content {
                padding: 15px 10px;
            }

            .congratulations {
                font-size: 16px;
                padding: 20px 15px;
            }

            .countdown-timer {
                font-size: 20px;
            }

            .detail-icon {
                width: 42px;
                height: 42px;
                font-size: 20px;
                line-height: 1;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .feature-icon {
                font-size: 36px;
            }

            .notice-title {
                font-size: 16px;
            }

            .footer {
                padding: 30px 15px;
            }

            .social-link {
                width: 40px;
                height: 40px;
                font-size: 18px;
                line-height: 1;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                margin: 0 6px;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-5px);
        }

        /* Print Styles */
        @media print {
            .email-container {
                box-shadow: none;
                border: 1px solid #ccc;
            }

            .header {
                background: var(--primary-color) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .cta-button {
                border: 2px solid var(--accent-color);
                color: var(--accent-color) !important;
                background: transparent !important;
            }

            .countdown, .social-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span class="wolf-icon">🐺</span>CYBER WOLF
            </div>
            <div class="tagline">Master Ethical Hacking & Cybersecurity</div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Congratulations Message -->
            <div class="congratulations">
                🎉 Congratulations! You're Successfully Registered 🎉<br>
                <strong>Cyber Wolf – Free Web Penetration Testing Class</strong>
            </div>
            
            <!-- Countdown Timer -->
            <div class="countdown">
                <div>⏰ Course Starts In:</div>
                <div class="countdown-timer" id="countdown">Loading...</div>
            </div>
            
            <!-- Course Details -->
            <div class="course-details">
                <div class="detail-item">
                    <div class="detail-icon">📚</div>
                    <div class="detail-content">
                        <div class="detail-label">Event Type</div>
                        <div class="detail-value">FREE TRAINING COURSE</div>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">🎯</div>
                    <div class="detail-content">
                        <div class="detail-label">Topic</div>
                        <div class="detail-value">Master Ethical Hacking & Cybersecurity with Industry Experts</div>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">🌐</div>
                    <div class="detail-content">
                        <div class="detail-label">Mode</div>
                        <div class="detail-value">Online (Join from anywhere)</div>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">📅</div>
                    <div class="detail-content">
                        <div class="detail-label">Date</div>
                        <div class="detail-value">18th July 2025 (Friday)</div>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-icon">⏰</div>
                    <div class="detail-content">
                        <div class="detail-label">Time</div>
                        <div class="detail-value">11:00 AM – 3:00 PM IST (4 Hours)</div>
                    </div>
                </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="cta-section">
                <a href="http://meet.google.com/twi-otot-prk" class="cta-button" target="_blank" rel="noopener">
                    🎥 Join Google Meet
                </a>
                <a href="https://cyberwolf-career-guidance.web.app/" class="cta-button secondary" target="_blank" rel="noopener">
                    🚀 Access Course Portal
                </a>
            
            </div>
            
            <!-- Features -->
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>Expert Guidance</h3>
                    <p>Learn from industry professionals with years of cybersecurity experience and real-world expertise</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">💻</div>
                    <h3>Hands-on Practice</h3>
                    <p>Get practical experience with real-world penetration testing scenarios and live demonstrations</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎓</div>
                    <h3>Career Boost</h3>
                    <p>Enhance your skills and advance your cybersecurity career with industry-recognized training</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <h3>Certification</h3>
                    <p>Receive a certificate of completion to showcase your new cybersecurity skills</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>Community</h3>
                    <p>Join a network of cybersecurity professionals and continue learning together</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>Tools & Resources</h3>
                    <p>Access professional-grade tools and resources used in the cybersecurity industry</p>
                </div>
            </div>
            
            <!-- Notice Board -->
            <div class="notice-board">
                <div class="notice-title">
                    📢 Important Course Information
                </div>
                <p><strong>What to Expect in This Training:</strong></p>
                <ul style="margin-left: 25px; margin-top: 15px;">
                    <li>💬 Live interactive sessions with expert Q&A</li>
                    <li>📥 Downloadable resources, tools, and study materials</li>
                    <li>🏅 Certificate of participation upon completion</li>
                    <li>🌐 Career guidance and networking opportunities</li>
                    <li>🤝 Hands-on demonstrations and practical exercises</li>
                    <li>❓ Access to course support and follow-up materials</li>
                </ul>

                <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 8px;">
                    <p><strong>⚠️ Pre-Course Preparation:</strong></p>
                    <p style="margin-top: 10px; font-size: 14px;">
                        • Ensure stable internet connection 🌐<br>
                        • Have a notebook ready for taking notes 📝<br>
                        • Join 5-10 minutes early for technical setup ⏰<br>
                        • Prepare questions you'd like to ask our experts ❓
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <h3>
                🐺 Powered By Cyber Wolf Team
            </h3>
            <p>Enhance your career. Boost your skills. Master cybersecurity.</p>
            <p style="font-style: italic; margin-top: 10px;">
                "Empowering the next generation of cybersecurity professionals"
            </p>

            <div class="social-links">
                <a href="mailto:<EMAIL>" class="social-link" title="Email Us">
                    📧
                </a>
                <a href="https://cyberwolf-career-guidance.web.app" class="social-link" title="Visit Website" target="_blank">
                    🌐
                </a>
                <a href="tel:+************" class="social-link" title="Call Us">
                    📱
                </a>
                <a href="https://www.linkedin.com/company/cyberwolf-team/" class="social-link" title="LinkedIn" target="_blank">
                    💼
                </a>
                <a href="https://x.com/Cyber_Wolf_Team?t=1EF061Edf4viWwPWJjHXdg&s=09" class="social-link" title="Twitter" target="_blank">
                    🐦
                </a>
               
            </div>

            <div style="margin: 25px 0; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <p><strong>Need Help?</strong></p>
                <p style="font-size: 14px; margin-top: 8px;">
                    Technical Support: <EMAIL><br>
                    Course Inquiries: <EMAIL><br>
                    Phone Support: +91 6374344424
                </p>
            </div>

            <div class="copyright">
                <p>© 2025 Cyber Wolf. All rights reserved.</p>
                <p>This is an automated email. Please do not reply directly to this message.</p>   
            </div>
        </div>
    </div>
    
    <script>
        // Enhanced Cyber Wolf Email Functionality
        class CyberWolfEmailEnhancer {
            constructor() {
                this.courseDate = new Date('2025-07-18T11:00:00+05:30');
                this.meetingLink = 'http://meet.google.com/twi-otot-prk';
                this.coursePortal = 'https://cyberwolf-career-guidance.web.app/';
                this.init();
            }

            init() {
                this.setupCountdown();
                this.setupButtonTracking();
                this.setupCalendarIntegration();
                this.setupShareFunctionality();
                this.setupNotifications();
                this.setupAnimations();
            }

            // Enhanced Countdown Timer
            setupCountdown() {
                const updateCountdown = () => {
                    const now = new Date();
                    const timeDiff = this.courseDate - now;
                    const countdownElement = document.getElementById('countdown');

                    if (!countdownElement) return;

                    if (timeDiff > 0) {
                        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

                        countdownElement.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;

                        // Notification alerts
                        if (days === 1 && hours === 0 && minutes === 0 && seconds === 0) {
                            this.showNotification('Course starts tomorrow!', 'info');
                        }
                        if (days === 0 && hours === 1 && minutes === 0 && seconds === 0) {
                            this.showNotification('Course starts in 1 hour!', 'urgent');
                        }
                    } else {
                        countdownElement.innerHTML = '🔴 Course has started!';
                        countdownElement.style.color = '#FF5252';
                    }
                };

                setInterval(updateCountdown, 1000);
                updateCountdown();
            }

            // Button Click Tracking
            setupButtonTracking() {
                document.querySelectorAll('.cta-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const buttonText = button.textContent.trim();
                        const timestamp = new Date().toISOString();

                        console.log('Button clicked:', buttonText, 'at', timestamp);
                        this.addClickFeedback(button);
                    });
                });
            }

            // Calendar Integration
            setupCalendarIntegration() {
                const calendarButton = document.getElementById('addToCalendar');
                if (calendarButton) {
                    calendarButton.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.addToCalendar();
                    });
                }
            }

            addToCalendar() {
                const startDate = this.courseDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
                const endDate = new Date(this.courseDate.getTime() + 4 * 60 * 60 * 1000)
                    .toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';

                const params = new URLSearchParams({
                    action: 'TEMPLATE',
                    text: 'Cyber Wolf - Free Web Penetration Testing Class',
                    dates: `${startDate}/${endDate}`,
                    details: `Join the free training course on Ethical Hacking & Cybersecurity.\\n\\nMeeting Link: ${this.meetingLink}\\nCourse Portal: ${this.coursePortal}`,
                    location: 'Online - Google Meet',
                    trp: 'false'
                });

                const calendarUrl = `https://calendar.google.com/calendar/render?${params.toString()}`;
                window.open(calendarUrl, '_blank');
            }

            // Share Functionality
            setupShareFunctionality() {
                const shareButton = document.getElementById('shareButton');
                if (shareButton) {
                    shareButton.addEventListener('click', () => {
                        this.shareEmail();
                    });
                }
            }

            shareEmail() {
                const shareData = {
                    title: 'Cyber Wolf - Free Training Course',
                    text: 'Join me for this free Ethical Hacking & Cybersecurity training!',
                    url: this.coursePortal
                };

                if (navigator.share) {
                    navigator.share(shareData);
                } else {
                    const subject = encodeURIComponent(shareData.title);
                    const body = encodeURIComponent(`${shareData.text}\\n\\n${shareData.url}`);
                    window.open(`mailto:?subject=${subject}&body=${body}`);
                }
            }

            // Notification System
            setupNotifications() {
                if ('Notification' in window && Notification.permission !== 'granted') {
                    Notification.requestPermission();
                }
            }

            showNotification(message, type = 'info') {
                // Browser notification
                if (Notification.permission === 'granted') {
                    new Notification('Cyber Wolf Training', {
                        body: message,
                        icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="50">🐺</text></svg>'
                    });
                }

                // In-page notification
                this.showInPageNotification(message, type);
            }

            showInPageNotification(message, type) {
                const notification = document.createElement('div');
                notification.innerHTML = `
                    <div style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: ${type === 'urgent' ? '#FF5252' : '#4CAF50'};
                        color: white;
                        padding: 15px 20px;
                        border-radius: 8px;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
                        z-index: 1000;
                        animation: slideIn 0.3s ease;
                        max-width: 300px;
                    ">
                        ${message}
                        <button onclick="this.parentElement.remove()" style="
                            background: none;
                            border: none;
                            color: white;
                            margin-left: 15px;
                            cursor: pointer;
                            font-size: 18px;
                            float: right;
                        ">×</button>
                    </div>
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }

            // Visual Feedback
            addClickFeedback(element) {
                element.style.transform = 'scale(0.95)';
                element.style.transition = 'transform 0.1s ease';

                setTimeout(() => {
                    element.style.transform = '';
                }, 150);
            }

            // Setup Animations
            setupAnimations() {
                // Intersection Observer for scroll animations
                if ('IntersectionObserver' in window) {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                entry.target.style.animationPlayState = 'running';
                            }
                        });
                    });

                    document.querySelectorAll('.feature-card, .detail-item').forEach(el => {
                        observer.observe(el);
                    });
                }
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new CyberWolfEmailEnhancer();
        });
    </script>
</body>
</html>
