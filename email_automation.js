// Email Automation Script for Cyber Wolf Training Course
// Enhanced functionality for email template

class CyberWolfEmailEnhancer {
    constructor() {
        this.courseDate = new Date('2025-07-18T11:00:00+05:30');
        this.meetingLink = 'http://meet.google.com/twi-otot-prk';
        this.coursePortal = 'https://cyberwolf-career-guidance.web.app/';
        this.init();
    }

    init() {
        this.setupCountdown();
        this.setupButtonTracking();
        this.setupCalendarIntegration();
        this.setupNotifications();
        this.setupResponsiveFeatures();
    }

    // Enhanced Countdown Timer with Notifications
    setupCountdown() {
        const countdownElement = document.getElementById('countdown');
        if (!countdownElement) return;

        const updateCountdown = () => {
            const now = new Date();
            const timeDiff = this.courseDate - now;

            if (timeDiff > 0) {
                const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

                countdownElement.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;

                // Notification alerts
                if (days === 1 && hours === 0 && minutes === 0 && seconds === 0) {
                    this.showNotification('Course starts tomorrow!', 'reminder');
                }
                if (days === 0 && hours === 1 && minutes === 0 && seconds === 0) {
                    this.showNotification('Course starts in 1 hour!', 'urgent');
                }
            } else {
                countdownElement.innerHTML = '🔴 Course has started!';
                countdownElement.style.color = '#FF5252';
            }
        };

        setInterval(updateCountdown, 1000);
        updateCountdown();
    }

    // Button Click Tracking and Analytics
    setupButtonTracking() {
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const buttonText = button.textContent.trim();
                const timestamp = new Date().toISOString();
                
                // Track button clicks
                this.trackEvent('button_click', {
                    button: buttonText,
                    timestamp: timestamp,
                    url: button.href
                });

                // Add visual feedback
                this.addClickFeedback(button);
            });
        });
    }

    // Calendar Integration
    setupCalendarIntegration() {
        const calendarButton = this.createCalendarButton();
        const ctaSection = document.querySelector('.cta-section');
        if (ctaSection) {
            ctaSection.appendChild(calendarButton);
        }
    }

    createCalendarButton() {
        const button = document.createElement('a');
        button.className = 'cta-button';
        button.innerHTML = '📅 Add to Calendar';
        button.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
        
        const calendarUrl = this.generateCalendarUrl();
        button.href = calendarUrl;
        button.target = '_blank';
        
        return button;
    }

    generateCalendarUrl() {
        const startDate = this.courseDate.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        const endDate = new Date(this.courseDate.getTime() + 4 * 60 * 60 * 1000)
            .toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        
        const params = new URLSearchParams({
            action: 'TEMPLATE',
            text: 'Cyber Wolf - Free Web Penetration Testing Class',
            dates: `${startDate}/${endDate}`,
            details: `Join the free training course on Ethical Hacking & Cybersecurity.\n\nMeeting Link: ${this.meetingLink}\nCourse Portal: ${this.coursePortal}`,
            location: 'Online - Google Meet',
            trp: 'false'
        });
        
        return `https://calendar.google.com/calendar/render?${params.toString()}`;
    }

    // Notification System
    setupNotifications() {
        if ('Notification' in window) {
            Notification.requestPermission();
        }
    }

    showNotification(message, type = 'info') {
        // Browser notification
        if (Notification.permission === 'granted') {
            new Notification('Cyber Wolf Training', {
                body: message,
                icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="50">🐺</text></svg>'
            });
        }

        // In-page notification
        this.showInPageNotification(message, type);
    }

    showInPageNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'urgent' ? '#FF5252' : '#4CAF50'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            ">
                ${message}
                <button onclick="this.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    margin-left: 10px;
                    cursor: pointer;
                    font-size: 16px;
                ">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Responsive Features
    setupResponsiveFeatures() {
        // Touch-friendly interactions for mobile
        if ('ontouchstart' in window) {
            document.querySelectorAll('.feature-card').forEach(card => {
                card.addEventListener('touchstart', () => {
                    card.style.transform = 'scale(0.95)';
                });
                
                card.addEventListener('touchend', () => {
                    card.style.transform = 'translateY(-5px)';
                });
            });
        }

        // Dynamic font sizing based on screen size
        this.adjustFontSizes();
        window.addEventListener('resize', () => this.adjustFontSizes());
    }

    adjustFontSizes() {
        const screenWidth = window.innerWidth;
        const root = document.documentElement;
        
        if (screenWidth < 480) {
            root.style.setProperty('--base-font-size', '14px');
        } else if (screenWidth < 768) {
            root.style.setProperty('--base-font-size', '15px');
        } else {
            root.style.setProperty('--base-font-size', '16px');
        }
    }

    // Event Tracking
    trackEvent(eventName, data) {
        // Console logging for development
        console.log(`Event: ${eventName}`, data);
        
        // Here you would typically send to analytics service
        // Example: Google Analytics, Mixpanel, etc.
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, data);
        }
    }

    // Visual Feedback
    addClickFeedback(element) {
        element.style.transform = 'scale(0.95)';
        element.style.transition = 'transform 0.1s ease';
        
        setTimeout(() => {
            element.style.transform = 'translateY(-2px)';
        }, 100);
    }

    // Email Sharing Functionality
    setupEmailSharing() {
        const shareButton = document.createElement('button');
        shareButton.className = 'cta-button';
        shareButton.innerHTML = '📤 Share Course';
        shareButton.onclick = () => this.shareEmail();
        
        const ctaSection = document.querySelector('.cta-section');
        if (ctaSection) {
            ctaSection.appendChild(shareButton);
        }
    }

    shareEmail() {
        const shareData = {
            title: 'Cyber Wolf - Free Training Course',
            text: 'Join me for this free Ethical Hacking & Cybersecurity training!',
            url: this.coursePortal
        };

        if (navigator.share) {
            navigator.share(shareData);
        } else {
            // Fallback to email sharing
            const subject = encodeURIComponent(shareData.title);
            const body = encodeURIComponent(`${shareData.text}\n\n${shareData.url}`);
            window.open(`mailto:?subject=${subject}&body=${body}`);
        }
    }

    // Accessibility Enhancements
    setupAccessibility() {
        // Add ARIA labels
        document.querySelectorAll('.cta-button').forEach(button => {
            button.setAttribute('role', 'button');
            button.setAttribute('aria-label', button.textContent);
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                if (e.target.classList.contains('cta-button')) {
                    e.target.click();
                }
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CyberWolfEmailEnhancer();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CyberWolfEmailEnhancer;
}
